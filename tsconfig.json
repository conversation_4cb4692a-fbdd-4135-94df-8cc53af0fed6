{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noUnusedLocals": false, "strict": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "allowJs": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "react", "react-dom"]}, "include": ["src", "src/types", "src/utils", "git-hook-setup.ts", "git-script.ts", "postcss.config.ts"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.e2e.ts", "dist", "dev-dist", "playwright-report", "test-results", "src/test/e2e/global-setup.ts"], "references": [{"path": "./tsconfig.node.json"}]}